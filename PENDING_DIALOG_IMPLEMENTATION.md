# PENDING状态弹框实现说明

## 功能概述
为合同风险判断页面添加了PENDING状态弹框功能，当用户充值状态为PENDING时，会显示一个无法手动关闭的弹框，提示用户充值正在进行中，并提供定时查询和手动重新查询功能。

## 实现的功能

### 1. PENDING状态弹框
- **触发条件**: 当支付状态为`PENDING`时自动显示
- **弹框特性**: 
  - 无法通过点击遮罩层关闭
  - 无法通过ESC键关闭
  - 无关闭按钮
  - 居中显示，宽度400px

### 2. 自动定时查询
- **查询频率**: 每30秒自动查询一次支付状态
- **静默处理**: 查询过程中不显示loading，不干扰用户体验
- **状态变化**: 当状态不再是PENDING时，自动关闭弹框并处理相应状态

### 3. 手动重新查询
- **查询按钮**: 弹框中提供"重新查询"按钮
- **Loading状态**: 点击查询时按钮显示loading状态
- **错误处理**: 查询失败时显示错误提示

### 4. 状态处理
- **SUCCESS**: 关闭弹框，显示成功消息，刷新数据，清除订单ID
- **FAILED**: 关闭弹框，显示失败消息，清除订单ID
- **CANCELED**: 关闭弹框，显示取消消息，清除订单ID
- **PENDING**: 显示弹框，开始定时查询

## 修改的文件

### 1. src/views/contractRiskJudgement/index.vue
**模板部分**:
- 添加了PENDING状态弹框的HTML结构
- 使用Element UI的el-dialog组件
- 配置了弹框的不可关闭属性

**脚本部分**:
- 添加了新的数据属性：
  - `showPendingDialog`: 控制弹框显示
  - `recheckLoading`: 控制重新查询按钮的loading状态
  - `pendingCheckInterval`: 存储定时器引用

- 重构了支付结果处理逻辑：
  - `handlePaymentStatus()`: 统一处理各种支付状态
  - `startPendingCheck()`: 开始定时查询
  - `checkPaymentStatusSilently()`: 静默查询支付状态
  - `recheckPaymentStatus()`: 手动重新查询
  - `closePendingDialog()`: 关闭弹框并清理定时器

- 添加了生命周期钩子：
  - `beforeDestroy()`: 组件销毁前清理定时器

**样式部分**:
- 添加了弹框的CSS样式
- 实现了loading图标的旋转动画
- 设置了弹框内容的居中布局和间距

### 2. 国际化文件
**src/lang/zh.js**:
- `paySuccess`: '支付成功'
- `payFail`: '支付失败'
- `payCanceled`: '支付已取消'
- `paymentProcessing`: '充值处理中'
- `paymentPendingTip`: '您的充值正在处理中，请耐心等待。我们会定期检查支付状态，一旦完成会自动更新。'
- `recheckPayment`: '重新查询'
- `checkPaymentFailed`: '查询支付状态失败，请稍后重试'

**src/lang/en.js**:
- 添加了对应的英文翻译

**src/lang/ja.js**:
- 添加了对应的日文翻译

## 技术细节

### 定时器管理
- 使用`setInterval`实现定时查询
- 在组件销毁、弹框关闭、状态变化时及时清理定时器
- 避免内存泄漏和重复定时器

### 错误处理
- 静默查询时捕获错误但不显示给用户
- 手动查询时显示错误提示
- 网络异常时不影响用户正常使用

### 用户体验
- 弹框无法意外关闭，确保用户知道充值状态
- 提供手动查询选项，给用户主动权
- 自动查询减少用户操作负担
- Loading状态提供视觉反馈

## 测试文件
创建了`test-pending-dialog.html`文件用于独立测试弹框功能，包含：
- 弹框显示测试
- 各种状态模拟
- 定时查询模拟
- 手动查询测试

## 使用方式
1. 用户进行充值操作
2. 如果支付状态返回PENDING，自动显示弹框
3. 系统每30秒自动查询一次状态
4. 用户可点击"重新查询"按钮手动查询
5. 状态变化时自动关闭弹框并显示相应消息

## 注意事项
- 定时器会在组件销毁时自动清理
- 弹框显示期间会阻止用户进行其他操作
- 支持多语言显示
- 兼容现有的支付流程
