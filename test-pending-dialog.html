<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PENDING弹框测试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <style>
        .pending-dialog .el-dialog__header {
            text-align: center;
            padding: 20px 20px 10px;
        }
        
        .pending-dialog-title {
            font-size: 16px;
            font-weight: 500;
            color: #409EFF;
        }
        
        .pending-dialog-title .el-icon-loading {
            margin-right: 8px;
            animation: rotating 2s linear infinite;
        }
        
        .pending-dialog-content {
            text-align: center;
            padding: 20px;
        }
        
        .pending-dialog-content p {
            margin-bottom: 20px;
            color: #606266;
            line-height: 1.5;
        }
        
        .pending-dialog-actions {
            margin-top: 20px;
        }
        
        @keyframes rotating {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }
        
        .test-buttons {
            margin: 20px;
            text-align: center;
        }
        
        .test-buttons button {
            margin: 10px;
            padding: 10px 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-buttons">
            <h2>PENDING状态弹框测试</h2>
            <button @click="showPending">显示PENDING弹框</button>
            <button @click="simulateSuccess">模拟支付成功</button>
            <button @click="simulateFailed">模拟支付失败</button>
        </div>

        <!-- PENDING状态弹框 -->
        <el-dialog
            :visible.sync="showPendingDialog"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :show-close="false"
            width="400px"
            center
            class="pending-dialog"
        >
            <div slot="title" class="pending-dialog-title">
                <i class="el-icon-loading"></i>
                充值处理中
            </div>
            <div class="pending-dialog-content">
                <p>您的充值正在处理中，请耐心等待。我们会定期检查支付状态，一旦完成会自动更新。</p>
                <div class="pending-dialog-actions">
                    <el-button 
                        type="primary" 
                        :loading="recheckLoading"
                        @click="recheckPaymentStatus"
                    >
                        重新查询
                    </el-button>
                </div>
            </div>
        </el-dialog>
    </div>

    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    showPendingDialog: false,
                    recheckLoading: false,
                    pendingCheckInterval: null
                }
            },
            methods: {
                showPending() {
                    this.showPendingDialog = true;
                    this.startPendingCheck();
                },
                simulateSuccess() {
                    this.handlePaymentStatus('SUCCESS');
                },
                simulateFailed() {
                    this.handlePaymentStatus('FAILED');
                },
                handlePaymentStatus(status) {
                    const callback = {
                        SUCCESS: () => {
                            this.closePendingDialog();
                            this.$message.success('支付成功');
                        },
                        FAILED: () => {
                            this.closePendingDialog();
                            this.$message.error('支付失败');
                        },
                        PENDING: () => {
                            this.showPendingDialog = true;
                            this.startPendingCheck();
                        },
                        CANCELED: () => {
                            this.closePendingDialog();
                            this.$message.info('支付已取消');
                        },
                    };
                    callback[status] && callback[status]();
                },
                startPendingCheck() {
                    if (this.pendingCheckInterval) {
                        clearInterval(this.pendingCheckInterval);
                    }
                    
                    // 模拟每5秒检查一次（实际项目中是30秒）
                    this.pendingCheckInterval = setInterval(() => {
                        console.log('自动检查支付状态...');
                        // 这里可以模拟随机成功
                        if (Math.random() > 0.8) {
                            this.handlePaymentStatus('SUCCESS');
                        }
                    }, 5000);
                },
                recheckPaymentStatus() {
                    this.recheckLoading = true;
                    
                    // 模拟API调用
                    setTimeout(() => {
                        // 随机返回结果
                        const statuses = ['SUCCESS', 'FAILED', 'PENDING'];
                        const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
                        this.handlePaymentStatus(randomStatus);
                        this.recheckLoading = false;
                    }, 1000);
                },
                closePendingDialog() {
                    this.showPendingDialog = false;
                    if (this.pendingCheckInterval) {
                        clearInterval(this.pendingCheckInterval);
                        this.pendingCheckInterval = null;
                    }
                }
            },
            beforeDestroy() {
                if (this.pendingCheckInterval) {
                    clearInterval(this.pendingCheckInterval);
                }
            }
        })
    </script>
</body>
</html>
