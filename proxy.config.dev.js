const fs = require('fs');
const path = require('path');

const targetMap = {
/* 线上环境start */
    'line': 'https://hubbledoc.ai',
    /* 线上环境end */

    /* 测试环境start */
    'ent': 'https://hubbledoc.net', // 预发布
    /* 测试环境end */

    /* 开发环境start */
    'dev1': 'https://hubbledoc.tech',
    /* 开发环境end */

};

// 重要：修改dev环境，改变根目录.env.proxy.local 文件即可【没有的话，自己建一个】， 支持直接使用地址、 targetMap中的枚举key
function getTarget() {
    let target = targetMap['dev1']; // 默认值
    try {
        let localTarget = '';
        const filePath = path.join(__dirname, './.env.proxy.local');
        const stat = fs.statSync(filePath);
        if (stat.isFile(filePath)) {
            const txt = fs.readFileSync(filePath, 'utf-8');
            localTarget = (txt || '').trim();
        }
        if (localTarget) {
            target = /^http/.test(localTarget) ? localTarget : targetMap[localTarget];
        }
    } catch (e) {
        console.log(e);
    }
    if (!target) {
        throw Error('dev proxy target指定error');
    }
    console.log(`dev本地代理地址为${target}`);
    return target;
}

module.exports = [
    {
        context: [
            '/ents',
            '/file',
            '/basis',
            '/users',
            '/demo-api',
            '/auth-center',
            '/octopus-api',
            '/contract-api',
            '/template-api',
            '/authenticated',
            '/api/auth-center',
            '/contract-export',
            '/contract-center',
            '/contract-center-bearing',
            '/dwh/customerMgrData',
            '/contract-search',
            '/ad-api',
            '/front/log',
            '/web-api',
        ],
        target: getTarget(),
        changeOrigin: true,
        secure: false,
        logLevel: 'silent',
    },
    {
        context: '/www',
        changeOrigin: true,
        // target: 'http://10.128.47.100:8137',
        target: 'https://www.bestsign.info',
        logLevel: 'silent', // debug模式可以确认代理结果
    },
];
