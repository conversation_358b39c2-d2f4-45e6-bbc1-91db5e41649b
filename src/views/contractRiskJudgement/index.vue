<template>
    <div class="contract-review risk-judgement">
        <Header ref="header"
            :config="{
                productType,
                toolType: ['协议风险判断', '协议风险判断(深度推理)', '协议要素提取',],
            }"
        ></Header>
        <div class="contract-review__head">
            <div class="contract-review__head-box">
                <div class="contract-review__head-left">
                    <el-tooltip :disabled="!leftName" :content="leftName" placement="bottom">
                        <span class="ellipsis">{{ $t('contractCompare.judgeTargetContract') }}：{{ leftName }}</span>
                    </el-tooltip>
                    <el-button :disabled="!leftDocReady || leftDocumentLoading" @click="clear">{{ $t('contractCompare.reUpload') }}</el-button>
                </div>
                <div class="contract-review__head-right">
                    <div class="right-tab">
                        <p>{{ $t('contractCompare.riskJudgement') }}</p>
                    </div>
                    <div>
                        <el-button
                            type="primary"
                            :loading="btnLoading"
                            :disabled="!analysisId || !leftDocReady || leftDocumentLoading || isHistory || rightRiskReady"
                            @click="startJudge"
                        >{{ $t('contractCompare.startJudge') }}</el-button>
                    </div>
                </div>
            </div>
            <div class="contract-review__result flex">
                <div class="head-tab selected-tab">
                    <div class="head-tab_title">{{ $t('contractCompare.history') }}</div>
                    <div class="head-tab_info">{{ $t('contractCompare.historyLog', {num: total}) }}</div>
                </div>
            </div>
        </div>
        <div class="contract-review__content">
            <div class="contract-review__content-box">
                <div class="contract-review__content-left" v-loading="leftLoading">
                    <div class="contract-review__content-left-mask" v-if="shouldGuideToRecharge" @click="showTipsAndCharge"></div>
                    <Upload
                        v-if="!leftDocReady"
                        :uploadUrl="uploadUrl"
                        @uploadSuccess="readyCompare"
                        @uploadFail="uploadFail"
                    ></Upload>
                    <template v-else>
                        <Document
                            v-if="documentType === 'doc'"
                            ref="leftDocument"
                            :fileUrl="downloadUrl"
                            :fragment="fragment"
                            :showShadowMode="true"
                            @changeLoading="(val) => leftDocumentLoading = val"
                            @changeFileName="(val) => leftName = val"
                        />
                    </template>
                </div>
                <div class="contract-review__content-right">
                    <AgentInfo
                        ref="risk"
                        :docAnalysisId="analysisId"
                        :rightRiskReady="rightRiskReady"
                        :hasHistoryEvent="true"
                        @updateHistory="getHistory"
                        @renderFragment="renderFragment"
                        @locateShadow="locateShadow"
                        @showCharge="handleCharge"
                    ></AgentInfo>
                </div>
            </div>
            <div class="contract-review__result">
                <div class="contract-review__result-content">
                    <div
                        class="contract-review__result-content-item history"
                        v-for="(his, i) in history"
                        :key="i"
                        @click="goHistory(his)"
                    >
                        <el-tooltip :content="his.agreementName" placement="top">
                            <p class="title ellipsis">{{ his.agreementName }}</p>
                        </el-tooltip>
                        <p class="info">
                            <span>{{ moment(his.analysisTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
                            <!-- <span>{{ getReviewStatus(his.taskStatus) }}</span> -->
                        </p>
                    </div>
                    <NoData v-if="!history.length" />
                </div>
                <el-pagination
                    small
                    layout="pager"
                    @current-change="handleCurrentChange"
                    :pager-count="5"
                    :page-size="10"
                    :current-page.sync="page"
                    :total="total"
                >
                </el-pagination>
            </div>
        </div>
        <RegisterFooter class="login-footer absolute-login-footer"></RegisterFooter>

        <!-- PENDING状态弹框 -->
        <el-dialog
            :visible.sync="showPendingDialog"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :show-close="false"
            width="400px"
            center
            class="pending-dialog"
        >
            <div slot="title" class="pending-dialog-title">
                <i class="el-icon-loading"></i>
                {{ $t('agent.paymentProcessing') }}
            </div>
            <div class="pending-dialog-content">
                <p>{{ $t('agent.paymentPendingTip') }}</p>
                <div class="pending-dialog-actions">
                    <el-button
                        type="primary"
                        :loading="recheckLoading"
                        @click="recheckPaymentStatus"
                    >
                        {{ $t('agent.recheckPayment') }}
                    </el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import moment from 'dayjs';
import Upload from './upload';
import Header from 'components/hubbleApplyHeader';
import RegisterFooter from 'components/register_footer';
import NoData from 'components/noData';
import Document from './document';
// import RiskInfo from './riskInfo';
import AgentInfo from 'components/agentInfoContent/index.vue';
import ChargeDialog from '@/components/charge/index.vue';
import {
    hubbleRiskHistory,
} from 'src/api/judgeRisk.js';
import { mapState } from 'vuex';

export default {
    components: { Upload, Header, RegisterFooter, NoData, Document, AgentInfo, ChargeDialog },
    data() {
        return {
            leftName: '',
            leftLoading: false,
            history: [],
            btnLoading: false,
            leftDocumentLoading: false,
            noDataText: this.$t('contractCompare.noData'),
            leftDocReady: false,
            rightRiskReady: false,
            isHistory: false,
            page: 1,
            total: 0,
            fragment: [],
            selectTab: '1',
            shouldGuideToRecharge: false,
            paymentPlanType: null,
            analysisId: '',
            showChargeDialog: false,
            productConfig: {
                title: this.$t('judgeRisk.title'),
                per: this.$t('hubblePackage.copy'),
                unitPer: this.$t('hubblePackage.copy'),
            },
            productType: [22, 23, 25],
            showPendingDialog: false,
            recheckLoading: false,
            pendingCheckInterval: null,
        };
    },
    computed: {
        ...mapState('hubble', ['documentType']),
        uploadUrl() {
            return `/web-api/agreement-analysis/upload-file-and-init`;
        },
        downloadUrl() {
            return `/web-api/agreement-analysis/${this.analysisId}/view-file`;
        },
    },
    methods: {
        moment,
        clear() {
            this.leftDocReady = false;
            this.$router.replace({ path: this.$route.path, query: { analysisId: '' } });
            this.isHistory = false;
            this.fragment = [];
            this.leftName = '';
            this.noDataText = '';
            this.analysisId = '';
            this.rightRiskReady = false;
            this.btnLoading = false;
            this.$refs.risk.reset();
        },
        readyCompare(res) {
            const { data, fileName } = res;
            this.analysisId = data.analysisId;
            this.$store.state.hubble.allowEdit = data.allowEditMode;
            this.$store.state.hubble.multiVersionFileId = data.editModeExtensionInfo?.multiVersionFileId;
            this.$router.replace({ path: this.$route.path, query: { analysisId: this.analysisId } });
            this.leftName = fileName;
            this.leftDocReady = true;
            // this.getHistory();
        },
        uploadFail() {
            setTimeout(() => {
                this.showChargeDialog = true;
            }, 1000);
        },
        startJudge() {
            this.rightRiskReady = true;
            this.$refs.risk.analysisId = this.analysisId;
            this.$refs.risk.initAnalyze();
        },
        getHistory() {
            hubbleRiskHistory({
                pageNum: this.page,
                pageSize: 10,
                toolType: 'RISK_JUDGEMENT',
            })
                .then((res) => {
                    const { records, totalRecord } = res.data;
                    this.history = records;
                    this.total = +totalRecord;
                });
        },
        goHistory(obj) {
            this.leftDocReady = false;
            this.rightRiskReady = true;
            this.analysisId = obj.analysisId;
            this.leftName = obj.agreementName;
            this.shouldGuideToRecharge = false;
            this.fragment = [];
            this.$refs.risk.reset();
            // this.$refs.risk.initAnalyze();
            this.$refs.risk.isInit = false;
            this.$refs.risk.getAnalysisRecord(this.analysisId);
            // 获取当前路由
            this.$router.replace({ path: this.$route.path, query: { analysisId: this.analysisId } });
            this.isHistory = true;
            this.$nextTick(() => {
                this.leftDocReady = true;
            });
        },
        handleCurrentChange(page) {
            this.page = page;
            this.getHistory();
        },
        showTipsAndCharge() {
            this.$MessageToast.info(this.$t('agent.chargeTip'));
            setTimeout(() => {
                this.showChargeDialog = true;
            }, 1000);
        },
        renderFragment(positions) {
            this.fragment = positions;
        },
        locateShadow(num) {
            this.$refs.leftDocument.locateShadow(num);
        },
        handleCharge() {
            this.$http.post('/web-api/create-checkout-session', {
                'priceId': 'price_1Rvq37COMUQVUUXCfdvwW1Fj',
                'quantity': 2,
                'successUrl': `${location.href}&fromStripe=1`,
                'cancelUrl': `${location.href}&fromStripe=0`,
            }).then(res => {
                const { orderId, url } = res.data;
                this.$localStorage.set('hubbleRechargeOrderId', orderId);
                location.href = url;
            });
        },
        getPayResult() {
            const orderId = this.$localStorage.get('hubbleRechargeOrderId');
            if (!orderId) {
                return;
            }
            this.$http.get(`/web-api/orders/${orderId}`).then(res => {
                const { status } = res.data;
                this.handlePaymentStatus(status);
            });
        },
        handlePaymentStatus(status) {
            const callback = {
                SUCCESS: () => {
                    this.closePendingDialog();
                    this.$refs.risk.historyAnalysisResults = [];
                    this.$refs.risk.getAnalysisRecord(this.analysisId);
                    this.$MessageToast.success(this.$t('agent.paySuccess'));
                    this.$localStorage.remove('hubbleRechargeOrderId');
                },
                FAILED: () => {
                    this.closePendingDialog();
                    this.$MessageToast.error(this.$t('agent.payFail'));
                    this.$localStorage.remove('hubbleRechargeOrderId');
                },
                PENDING: () => {
                    this.showPendingDialog = true;
                    this.startPendingCheck();
                },
                CANCELED: () => {
                    this.closePendingDialog();
                    this.$MessageToast.info(this.$t('agent.payCanceled'));
                    this.$localStorage.remove('hubbleRechargeOrderId');
                },
            };
            callback[status] && callback[status]();
        },
        startPendingCheck() {
            // 清除之前的定时器
            if (this.pendingCheckInterval) {
                clearInterval(this.pendingCheckInterval);
            }

            // 每30秒自动检查一次支付状态
            this.pendingCheckInterval = setInterval(() => {
                this.checkPaymentStatusSilently();
            }, 30000);
        },
        checkPaymentStatusSilently() {
            const orderId = this.$localStorage.get('hubbleRechargeOrderId');
            if (!orderId) {
                this.closePendingDialog();
                return;
            }

            this.$http.get(`/web-api/orders/${orderId}`).then(res => {
                const { status } = res.data;
                if (status !== 'PENDING') {
                    this.handlePaymentStatus(status);
                }
            }).catch(() => {
                // 静默处理错误，不影响用户体验
            });
        },
        recheckPaymentStatus() {
            this.recheckLoading = true;
            const orderId = this.$localStorage.get('hubbleRechargeOrderId');
            if (!orderId) {
                this.closePendingDialog();
                this.recheckLoading = false;
                return;
            }

            this.$http.get(`/web-api/orders/${orderId}`).then(res => {
                const { status } = res.data;
                this.handlePaymentStatus(status);
            }).catch(() => {
                this.$MessageToast.error(this.$t('agent.checkPaymentFailed'));
            }).finally(() => {
                this.recheckLoading = false;
            });
        },
        closePendingDialog() {
            this.showPendingDialog = false;
            if (this.pendingCheckInterval) {
                clearInterval(this.pendingCheckInterval);
                this.pendingCheckInterval = null;
            }
        },
    },
    mounted() {
        this.analysisId = this.$route.query.analysisId;
        if (this.analysisId) {
            this.leftDocReady = false;
            this.rightRiskReady = true;
            this.shouldGuideToRecharge = false;
            this.$refs.risk.reset();
            this.$refs.risk.getAnalysisRecord(this.analysisId);
            this.isHistory = true;
            this.$nextTick(() => {
                this.leftDocReady = true;
            });
        }
    },
    created() {
        if (this.$route.query.fromStripe === '1') {
            this.getPayResult();
        } else if (this.$route.query.fromStripe === '0') {
            this.$MessageToast.error(this.$t('agent.payFail'));
        }
        this.getHistory();
    },
    beforeDestroy() {
        // 清理定时器
        if (this.pendingCheckInterval) {
            clearInterval(this.pendingCheckInterval);
        }
    },
};
</script>

<style lang="scss">
@import './index.scss';
</style>
<style lang="scss" scoped>
.right-tab::v-deep .el-tabs__item {
    padding: 0 4px;
}
.risk-judgement{
    .contract-review__head{
        &-left{
            width: 60%;
        }
        &-right{
            width: 40%;
        }
    }
    .contract-review__content{
        &-left {
            position: relative;
            width: 60%;
            &-mask {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 999;
                cursor: pointer;
            }
        }
        &-right{
            width: 40%;
        }
    }
}
</style>
