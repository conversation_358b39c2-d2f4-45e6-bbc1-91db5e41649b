<!-- 业务/基础组件： -->
<template>
    <footer class="register-footer">
        <ul class="clear font-size-zero">
            <li v-if="isCanSwitchLanguage" class="lang-switch-btn">
                <LangSwitch :cacheOnly="langCacheOnly"></LangSwitch>
                <i>|</i>
            </li>
            <li class="about">
                <a target="_blank" :href="`https://www.bestsign.com/about-us`">
                    <span>{{ $t('commonFooter.aboutBestSign') }}</span>
                </a>
                <i>|</i>
            </li>
            <li class="copyright">
                <span>V4.0.0 {{ $t('commonFooter.copyright') }} © {{ backEndCopyRightRange || defaultCopyRightRange }} {{ $t('commonFooter.company') }}</span>
            </li>
        </ul>
    </footer>
</template>
<script>
import LangSwitch from 'components/langSwitch';
import { mapState } from 'vuex';

export default {
    name: 'JaFooter',
    components: {
        LangSwitch,
    },
    props: {
        langCacheOnly: {
            default: true,
            type: <PERSON>olean,
        },
    },
    data() {
        return {
            backEndCopyRightRange: this.$cookie.get('copyRightRange'),
            defaultCopyRightRange: `2014-${new Date().getFullYear()}`,
        };
    },
    computed: {
        ...mapState(['features']),
        isCanSwitchLanguage() { // 是否可以切换语言
            return true;
            // return this.features.includes('180');
        },
    },
    methods: {
    },
};
</script>
<style lang="scss">
	$border-color: #ddd;
	footer.register-footer {
        box-sizing: border-box;
		width: 100%;
		height: 35px;
		// line-height: 35px;
		padding-top: 10px;
		// padding-bottom: 15px;
		border-top: 1px solid $border-color;
        background-color: #f6f6f6;
		ul {
			display: block;
			width: 100%;
			text-align: center;
			white-space: nowrap;
			overflow: hidden;
			li {
				display: inline-block;
				font-size: 12px;
				color: #666;
                cursor: pointer;
				i {
					display: inline-block;
					margin: 0 5px;
					color: #d6d6d6;
				}

				span:last-child {
					color: #999;
				}

				i:last-child {
					margin: 0 10px;
				}

                &.lang-switch-btn {
                    span {
                        font-size: 12px;
                        color: #999999;
                    }
                    i.el-icon-ssq-diqiu {
                        margin: 0;
                        padding-right: 5px;
                        vertical-align: bottom;
                    }
                }
			}
		}
	}

</style>
