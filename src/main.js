// 兼容IE
import 'core-js/stable';
import 'regenerator-runtime/runtime';

import Vue from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';

import 'src/http';
import 'src/styles/index.scss';
import 'iconfont/iconfont.css';
import 'plugins/token';
import 'plugins/cookie';
import 'plugins/auth0';
import _global from 'const/global.js';
import MessageToastNew from 'components/messageToast/MessageToastNew.js';

// 全局变量
// vue文件使用全局变量方法：this.GLOBAL.变量名
Vue.prototype.GLOBAL = _global;
Vue.GLOBAL = _global;

// 提示框
Vue.prototype.$MessageToast = MessageToastNew;
Vue.$MessageToast = MessageToastNew;
import i18n from 'lang';
import ElementLocale from 'element-ui/lib/locale';
ElementLocale.i18n((key, value) => i18n.t(key, value));

import 'components/index.js';

window.addEventListener('load', function() {
    new Vue({
        router,
        store,
        i18n,
        render: createEle => createEle(App),
    }).$mount('#app');
});
